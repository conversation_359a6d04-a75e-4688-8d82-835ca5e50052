<?php

// Routes Configuration

// Home routes
$router->get('/', 'HomeController@index');

// Admin routes
$router->get('/admin', 'AdminController@dashboard', ['AuthMiddleware']);
$router->get('/admin/dashboard', 'AdminController@dashboard', ['AuthMiddleware']);

// Admin authentication routes
$router->get('/admin/login', 'AdminController@showLogin');
$router->post('/admin/login', 'AdminController@login');
$router->get('/admin/logout', 'AdminController@logout');

// Admin user management
$router->get('/admin/users', 'AdminController@users', ['AuthMiddleware']);
$router->get('/admin/users/create', 'AdminController@createUser', ['AuthMiddleware']);
$router->post('/admin/users/store', 'AdminController@storeUser', ['AuthMiddleware']);
$router->get('/admin/users/{id}/edit', 'AdminController@editUser', ['AuthMiddleware']);
$router->post('/admin/users/{id}/update', 'AdminController@updateUser', ['AuthMiddleware']);
$router->post('/admin/users/{id}/delete', 'AdminController@deleteUser', ['AuthMiddleware']);

// Admin settings
$router->get('/admin/settings', 'AdminController@settings', ['AuthMiddleware']);
$router->post('/admin/settings/update', 'AdminController@updateSettings', ['AuthMiddleware']);

// API routes
$router->get('/api/users', 'ApiController@users', ['AuthMiddleware']);
$router->post('/api/users', 'ApiController@createUser', ['AuthMiddleware']);
$router->get('/api/users/{id}', 'ApiController@getUser', ['AuthMiddleware']);
$router->put('/api/users/{id}', 'ApiController@updateUser', ['AuthMiddleware']);
$router->delete('/api/users/{id}', 'ApiController@deleteUser', ['AuthMiddleware']);
