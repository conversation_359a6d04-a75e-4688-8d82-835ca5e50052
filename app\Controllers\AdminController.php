<?php

class AdminController extends Controller
{
    private $auth;
    private $userModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->auth = Auth::getInstance();
        $this->userModel = $this->loadModel('User');
    }
    
    /**
     * Show login form
     */
    public function showLogin()
    {
        // If already logged in, redirect to dashboard
        if ($this->auth->check()) {
            $this->redirect('/admin/dashboard');
        }
        
        $data = [
            'title' => 'Admin Login',
            'error' => $this->getGet('error'),
            'expired' => $this->getGet('expired')
        ];
        
        // Login sayfası için özel render
        extract($data);
        include APP_PATH . '/Views/admin/login.php';
    }
    
    /**
     * Handle login
     */
    public function login()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/admin/login');
        }
        
        $email = $this->getPost('email');
        $password = $this->getPost('password');
        $remember = $this->getPost('remember') === 'on';
        
        if (empty($email) || empty($password)) {
            $this->redirect('/admin/login?error=missing_fields');
        }
        
        $result = $this->auth->login($email, $password, $remember);
        
        if ($result['success']) {
            $this->redirect('/admin/dashboard');
        } else {
            $this->redirect('/admin/login?error=invalid_credentials');
        }
    }
    
    /**
     * Handle logout
     */
    public function logout()
    {
        $this->auth->logout();
        $this->redirect('/admin/login');
    }
    
    /**
     * Admin dashboard
     */
    public function dashboard()
    {
        $this->requireAuth();
        
        $stats = $this->userModel->getStatistics();
        
        $data = [
            'title' => 'Dashboard',
            'user' => $this->getCurrentUser(),
            'stats' => $stats,
            'csrf_token' => $this->generateCsrfToken()
        ];
        
        $this->view('admin/dashboard', $data, 'admin');
    }
    
    /**
     * Users management
     */
    public function users()
    {
        $this->requireAuth();
        
        $page = (int) $this->getGet('page', 1);
        $search = $this->getGet('search', '');
        $perPage = 10;
        
        $users = $this->userModel->getAllUsers($page, $perPage, $search);
        $totalUsers = $this->userModel->getTotalUsers($search);
        $totalPages = ceil($totalUsers / $perPage);
        
        $data = [
            'title' => 'Users Management',
            'users' => $users,
            'currentPage' => $page,
            'totalPages' => $totalPages,
            'search' => $search,
            'csrf_token' => $this->generateCsrfToken()
        ];
        
        $this->view('admin/users/index', $data, 'admin');
    }
    
    /**
     * Show create user form
     */
    public function createUser()
    {
        $this->requireAuth();
        
        $data = [
            'title' => 'Create User',
            'csrf_token' => $this->generateCsrfToken()
        ];
        
        $this->view('admin/users/create', $data, 'admin');
    }
    
    /**
     * Store new user
     */
    public function storeUser()
    {
        $this->requireAuth();
        $this->validateCsrf();
        
        $name = $this->getPost('name');
        $email = $this->getPost('email');
        $password = $this->getPost('password');
        $role = $this->getPost('role', 'user');
        $status = $this->getPost('status', 'active');
        
        // Validation
        $errors = [];
        if (empty($name)) $errors[] = 'Name is required';
        if (empty($email)) $errors[] = 'Email is required';
        if (empty($password)) $errors[] = 'Password is required';
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) $errors[] = 'Invalid email format';
        if ($this->userModel->emailExists($email)) $errors[] = 'Email already exists';
        
        if (!empty($errors)) {
            $this->json(['success' => false, 'errors' => $errors], 400);
        }
        
        $userId = $this->userModel->createUser([
            'name' => $name,
            'email' => $email,
            'password' => $password,
            'role' => $role,
            'status' => $status
        ]);
        
        if ($userId) {
            $this->json(['success' => true, 'message' => 'User created successfully']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to create user'], 500);
        }
    }
    
    /**
     * Show edit user form
     */
    public function editUser($id)
    {
        $this->requireAuth();
        
        $user = $this->userModel->find($id);
        if (!$user) {
            $this->redirect('/admin/users');
        }
        
        $data = [
            'title' => 'Edit User',
            'user' => $user,
            'csrf_token' => $this->generateCsrfToken()
        ];
        
        $this->view('admin/users/edit', $data, 'admin');
    }
    
    /**
     * Update user
     */
    public function updateUser($id)
    {
        $this->requireAuth();
        $this->validateCsrf();
        
        $user = $this->userModel->find($id);
        if (!$user) {
            $this->json(['success' => false, 'message' => 'User not found'], 404);
        }
        
        $name = $this->getPost('name');
        $email = $this->getPost('email');
        $password = $this->getPost('password');
        $role = $this->getPost('role');
        $status = $this->getPost('status');
        
        // Validation
        $errors = [];
        if (empty($name)) $errors[] = 'Name is required';
        if (empty($email)) $errors[] = 'Email is required';
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) $errors[] = 'Invalid email format';
        if ($this->userModel->emailExists($email, $id)) $errors[] = 'Email already exists';
        
        if (!empty($errors)) {
            $this->json(['success' => false, 'errors' => $errors], 400);
        }
        
        $updateData = [
            'name' => $name,
            'email' => $email,
            'role' => $role,
            'status' => $status
        ];
        
        if (!empty($password)) {
            $updateData['password'] = $password;
        }
        
        $result = $this->userModel->updateUser($id, $updateData);
        
        if ($result) {
            $this->json(['success' => true, 'message' => 'User updated successfully']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to update user'], 500);
        }
    }
    
    /**
     * Delete user
     */
    public function deleteUser($id)
    {
        $this->requireAuth();
        $this->validateCsrf();
        
        // Don't allow deleting current user
        if ($id == $this->auth->id()) {
            $this->json(['success' => false, 'message' => 'Cannot delete your own account'], 400);
        }
        
        $result = $this->userModel->delete($id);
        
        if ($result) {
            $this->json(['success' => true, 'message' => 'User deleted successfully']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to delete user'], 500);
        }
    }
    
    /**
     * Settings page
     */
    public function settings()
    {
        $this->requireAuth();
        
        $data = [
            'title' => 'Settings',
            'user' => $this->getCurrentUser(),
            'csrf_token' => $this->generateCsrfToken()
        ];
        
        $this->view('admin/settings', $data, 'admin');
    }
}
