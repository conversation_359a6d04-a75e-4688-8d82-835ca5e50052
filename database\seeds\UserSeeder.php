<?php

class UserSeeder
{
    private $db;
    
    public function __construct()
    {
        $this->db = Database::getInstance();
    }
    
    public function run()
    {
        echo "Seeding users table...\n";
        
        // Check if admin user already exists
        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM users WHERE email = ?");
        $stmt->execute(['<EMAIL>']);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result['count'] == 0) {
            // Create admin user
            $adminData = [
                'name' => 'Administrator',
                'email' => '<EMAIL>',
                'password' => password_hash('admin123', PASSWORD_DEFAULT),
                'role' => 'admin',
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $stmt = $this->db->prepare("
                INSERT INTO users (name, email, password, role, status, created_at, updated_at) 
                VALUES (:name, :email, :password, :role, :status, :created_at, :updated_at)
            ");
            
            if ($stmt->execute($adminData)) {
                echo "✓ Admin user created successfully\n";
                echo "  Email: <EMAIL>\n";
                echo "  Password: admin123\n";
            } else {
                echo "✗ Failed to create admin user\n";
            }
        } else {
            echo "✓ Admin user already exists\n";
        }
        
        // Create sample users
        $sampleUsers = [
            [
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'password' => password_hash('password123', PASSWORD_DEFAULT),
                'role' => 'user',
                'status' => 'active'
            ],
            [
                'name' => 'Jane Smith',
                'email' => '<EMAIL>',
                'password' => password_hash('password123', PASSWORD_DEFAULT),
                'role' => 'user',
                'status' => 'active'
            ],
            [
                'name' => 'Bob Wilson',
                'email' => '<EMAIL>',
                'password' => password_hash('password123', PASSWORD_DEFAULT),
                'role' => 'user',
                'status' => 'inactive'
            ]
        ];
        
        foreach ($sampleUsers as $userData) {
            // Check if user already exists
            $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM users WHERE email = ?");
            $stmt->execute([$userData['email']]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($result['count'] == 0) {
                $userData['created_at'] = date('Y-m-d H:i:s');
                $userData['updated_at'] = date('Y-m-d H:i:s');
                
                $stmt = $this->db->prepare("
                    INSERT INTO users (name, email, password, role, status, created_at, updated_at) 
                    VALUES (:name, :email, :password, :role, :status, :created_at, :updated_at)
                ");
                
                if ($stmt->execute($userData)) {
                    echo "✓ Sample user created: {$userData['email']}\n";
                } else {
                    echo "✗ Failed to create user: {$userData['email']}\n";
                }
            } else {
                echo "✓ User already exists: {$userData['email']}\n";
            }
        }
        
        echo "User seeding completed!\n\n";
    }
}
