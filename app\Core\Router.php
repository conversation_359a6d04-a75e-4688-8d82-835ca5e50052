<?php

class Router
{
    private $routes = [];
    private $middlewares = [];
    
    public function get($path, $callback, $middleware = [])
    {
        $this->addRoute('GET', $path, $callback, $middleware);
    }
    
    public function post($path, $callback, $middleware = [])
    {
        $this->addRoute('POST', $path, $callback, $middleware);
    }
    
    public function put($path, $callback, $middleware = [])
    {
        $this->addRoute('PUT', $path, $callback, $middleware);
    }
    
    public function delete($path, $callback, $middleware = [])
    {
        $this->addRoute('DELETE', $path, $callback, $middleware);
    }
    
    private function addRoute($method, $path, $callback, $middleware = [])
    {
        $this->routes[] = [
            'method' => $method,
            'path' => $path,
            'callback' => $callback,
            'middleware' => $middleware
        ];
    }
    
    public function handleRequest()
    {
        $requestMethod = $_SERVER['REQUEST_METHOD'];
        $requestUri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        
        // Remove base path if exists
        $basePath = dirname($_SERVER['SCRIPT_NAME']);
        if ($basePath !== '/') {
            $requestUri = substr($requestUri, strlen($basePath));
        }
        
        foreach ($this->routes as $route) {
            if ($route['method'] === $requestMethod && $this->matchPath($route['path'], $requestUri)) {
                // Execute middleware
                foreach ($route['middleware'] as $middleware) {
                    $middlewareInstance = new $middleware();
                    if (!$middlewareInstance->handle()) {
                        return;
                    }
                }
                
                // Execute callback
                if (is_string($route['callback'])) {
                    $this->executeControllerAction($route['callback'], $requestUri, $route['path']);
                } else {
                    call_user_func($route['callback']);
                }
                return;
            }
        }
        
        // 404 Not Found
        http_response_code(404);
        echo "404 - Page Not Found";
    }
    
    private function matchPath($routePath, $requestUri)
    {
        // Convert route path to regex pattern
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $routePath);
        $pattern = '#^' . $pattern . '$#';
        
        return preg_match($pattern, $requestUri);
    }
    
    private function executeControllerAction($callback, $requestUri, $routePath)
    {
        list($controller, $action) = explode('@', $callback);
        
        if (class_exists($controller)) {
            $controllerInstance = new $controller();
            
            // Extract parameters from URL
            $params = $this->extractParams($routePath, $requestUri);
            
            if (method_exists($controllerInstance, $action)) {
                call_user_func_array([$controllerInstance, $action], $params);
            } else {
                throw new Exception("Method {$action} not found in {$controller}");
            }
        } else {
            throw new Exception("Controller {$controller} not found");
        }
    }
    
    private function extractParams($routePath, $requestUri)
    {
        $routeParts = explode('/', trim($routePath, '/'));
        $uriParts = explode('/', trim($requestUri, '/'));
        
        $params = [];
        for ($i = 0; $i < count($routeParts); $i++) {
            if (preg_match('/\{([^}]+)\}/', $routeParts[$i])) {
                $params[] = $uriParts[$i] ?? null;
            }
        }
        
        return $params;
    }
}
