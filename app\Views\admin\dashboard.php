<div class="row">
    <!-- Statistics Cards -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Users
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= $stats['total'] ?? 0 ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Active Users
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= $stats['active'] ?? 0 ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Administrators
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= $stats['admins'] ?? 0 ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-shield fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Inactive Users
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= $stats['inactive'] ?? 0 ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-times fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Welcome Section -->
<div class="row">
    <div class="col-lg-8 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    Welcome to Zeppelin MVC Admin Panel
                </h6>
            </div>
            <div class="card-body">
                <p class="mb-3">
                    Welcome back, <strong><?= $this->escape($user['name']) ?></strong>! 
                    You are logged in as <span class="badge bg-primary"><?= $this->escape($user['role']) ?></span>.
                </p>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="font-weight-bold">Quick Actions:</h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <a href="<?= $this->url('admin/users') ?>" class="text-decoration-none">
                                    <i class="fas fa-users text-primary me-2"></i>
                                    Manage Users
                                </a>
                            </li>
                            <li class="mb-2">
                                <a href="<?= $this->url('admin/users/create') ?>" class="text-decoration-none">
                                    <i class="fas fa-user-plus text-success me-2"></i>
                                    Add New User
                                </a>
                            </li>
                            <li class="mb-2">
                                <a href="<?= $this->url('admin/settings') ?>" class="text-decoration-none">
                                    <i class="fas fa-cog text-info me-2"></i>
                                    System Settings
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="font-weight-bold">System Info:</h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-server text-muted me-2"></i>
                                PHP Version: <?= PHP_VERSION ?>
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-database text-muted me-2"></i>
                                Framework: Zeppelin MVC
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-clock text-muted me-2"></i>
                                Last Login: <?= date('M d, Y H:i') ?>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="col-lg-4 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list me-2"></i>
                    Recent Activity
                </h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item d-flex justify-content-between align-items-center border-0 px-0">
                        <div>
                            <i class="fas fa-sign-in-alt text-success me-2"></i>
                            <small>Admin login</small>
                        </div>
                        <small class="text-muted">Just now</small>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center border-0 px-0">
                        <div>
                            <i class="fas fa-cog text-info me-2"></i>
                            <small>System initialized</small>
                        </div>
                        <small class="text-muted">Today</small>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center border-0 px-0">
                        <div>
                            <i class="fas fa-rocket text-primary me-2"></i>
                            <small>Zeppelin MVC deployed</small>
                        </div>
                        <small class="text-muted">Today</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.text-gray-800 {
    color: #5a5c69 !important;
}
.text-gray-300 {
    color: #dddfeb !important;
}
</style>
