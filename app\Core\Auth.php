<?php

class Auth
{
    private static $instance = null;
    private $userModel;
    
    private function __construct()
    {
        $this->userModel = new User();
    }
    
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Attempt to log in a user
     */
    public function login($email, $password, $remember = false)
    {
        $user = $this->userModel->where(['email' => $email]);
        
        if (!empty($user) && password_verify($password, $user[0]['password'])) {
            $userData = $user[0];
            
            // Check if user is active
            if ($userData['status'] !== 'active') {
                return ['success' => false, 'message' => 'Account is not active'];
            }
            
            // Set session data
            $_SESSION['user_id'] = $userData['id'];
            $_SESSION['user_email'] = $userData['email'];
            $_SESSION['user_name'] = $userData['name'];
            $_SESSION['user_role'] = $userData['role'];
            $_SESSION['last_activity'] = time();
            
            // Generate CSRF token
            $_SESSION['_token'] = bin2hex(random_bytes(32));
            
            // Update last login
            $this->userModel->update($userData['id'], [
                'last_login' => date('Y-m-d H:i:s'),
                'login_count' => ($userData['login_count'] ?? 0) + 1
            ]);
            
            // Handle remember me
            if ($remember) {
                $this->setRememberToken($userData['id']);
            }
            
            return ['success' => true, 'user' => $userData];
        }
        
        return ['success' => false, 'message' => 'Invalid credentials'];
    }
    
    /**
     * Log out the current user
     */
    public function logout()
    {
        // Clear remember token if exists
        if (isset($_COOKIE['remember_token'])) {
            $this->clearRememberToken();
        }
        
        // Destroy session
        session_destroy();
        
        // Clear session cookie
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
        
        return true;
    }
    
    /**
     * Check if user is authenticated
     */
    public function check()
    {
        return isset($_SESSION['user_id']);
    }
    
    /**
     * Get current authenticated user
     */
    public function user()
    {
        if ($this->check()) {
            return $this->userModel->find($_SESSION['user_id']);
        }
        return null;
    }
    
    /**
     * Get current user ID
     */
    public function id()
    {
        return $_SESSION['user_id'] ?? null;
    }
    
    /**
     * Check if user has specific role
     */
    public function hasRole($role)
    {
        return isset($_SESSION['user_role']) && $_SESSION['user_role'] === $role;
    }
    
    /**
     * Check if user is admin
     */
    public function isAdmin()
    {
        return $this->hasRole('admin');
    }
    
    /**
     * Set remember token
     */
    private function setRememberToken($userId)
    {
        $token = bin2hex(random_bytes(32));
        $hashedToken = hash('sha256', $token);
        
        // Store hashed token in database
        $this->userModel->update($userId, [
            'remember_token' => $hashedToken,
            'remember_token_expires' => date('Y-m-d H:i:s', time() + (30 * 24 * 60 * 60)) // 30 days
        ]);
        
        // Set cookie
        setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/', '', false, true);
    }
    
    /**
     * Clear remember token
     */
    private function clearRememberToken()
    {
        if (isset($_SESSION['user_id'])) {
            $this->userModel->update($_SESSION['user_id'], [
                'remember_token' => null,
                'remember_token_expires' => null
            ]);
        }
        
        setcookie('remember_token', '', time() - 3600, '/');
    }
    
    /**
     * Attempt login via remember token
     */
    public function loginViaRememberToken()
    {
        if (!isset($_COOKIE['remember_token'])) {
            return false;
        }
        
        $token = $_COOKIE['remember_token'];
        $hashedToken = hash('sha256', $token);
        
        $user = $this->userModel->where(['remember_token' => $hashedToken]);
        
        if (!empty($user)) {
            $userData = $user[0];
            
            // Check if token is not expired
            if ($userData['remember_token_expires'] && 
                strtotime($userData['remember_token_expires']) > time()) {
                
                // Log in user
                $_SESSION['user_id'] = $userData['id'];
                $_SESSION['user_email'] = $userData['email'];
                $_SESSION['user_name'] = $userData['name'];
                $_SESSION['user_role'] = $userData['role'];
                $_SESSION['last_activity'] = time();
                $_SESSION['_token'] = bin2hex(random_bytes(32));
                
                return true;
            } else {
                // Token expired, clear it
                $this->clearRememberToken();
            }
        }
        
        return false;
    }
}
