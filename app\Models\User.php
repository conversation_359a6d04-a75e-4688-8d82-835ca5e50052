<?php

class User extends Model
{
    protected $table = 'users';
    protected $primaryKey = 'id';
    protected $fillable = [
        'name', 'email', 'password', 'role', 'status', 
        'last_login', 'login_count', 'remember_token', 
        'remember_token_expires', 'created_at', 'updated_at'
    ];
    protected $hidden = ['password', 'remember_token'];
    
    /**
     * Create a new user
     */
    public function createUser($data)
    {
        // Hash password
        if (isset($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }
        
        // Set default values
        $data['role'] = $data['role'] ?? 'user';
        $data['status'] = $data['status'] ?? 'active';
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->create($data);
    }
    
    /**
     * Update user
     */
    public function updateUser($id, $data)
    {
        // Hash password if provided
        if (isset($data['password']) && !empty($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        } else {
            unset($data['password']);
        }
        
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->update($id, $data);
    }
    
    /**
     * Find user by email
     */
    public function findByEmail($email)
    {
        $users = $this->where(['email' => $email]);
        return !empty($users) ? $users[0] : null;
    }
    
    /**
     * Check if email exists
     */
    public function emailExists($email, $excludeId = null)
    {
        $conditions = ['email' => $email];
        $users = $this->where($conditions);
        
        if ($excludeId) {
            $users = array_filter($users, function($user) use ($excludeId) {
                return $user['id'] != $excludeId;
            });
        }
        
        return !empty($users);
    }
    
    /**
     * Get all users with pagination
     */
    public function getAllUsers($page = 1, $perPage = 10, $search = '')
    {
        $offset = ($page - 1) * $perPage;
        
        if (!empty($search)) {
            $sql = "SELECT * FROM {$this->table} 
                    WHERE name LIKE ? OR email LIKE ? 
                    ORDER BY created_at DESC 
                    LIMIT {$perPage} OFFSET {$offset}";
            $params = ["%{$search}%", "%{$search}%"];
        } else {
            $sql = "SELECT * FROM {$this->table} 
                    ORDER BY created_at DESC 
                    LIMIT {$perPage} OFFSET {$offset}";
            $params = [];
        }
        
        $users = $this->query($sql, $params);
        
        // Hide sensitive fields
        foreach ($users as &$user) {
            $user = $this->hideFields($user);
        }
        
        return $users;
    }
    
    /**
     * Get total users count
     */
    public function getTotalUsers($search = '')
    {
        if (!empty($search)) {
            $sql = "SELECT COUNT(*) as count FROM {$this->table} 
                    WHERE name LIKE ? OR email LIKE ?";
            $params = ["%{$search}%", "%{$search}%"];
        } else {
            $sql = "SELECT COUNT(*) as count FROM {$this->table}";
            $params = [];
        }
        
        $result = $this->query($sql, $params);
        return (int) $result[0]['count'];
    }
    
    /**
     * Get users by role
     */
    public function getUsersByRole($role)
    {
        $users = $this->where(['role' => $role]);
        
        // Hide sensitive fields
        foreach ($users as &$user) {
            $user = $this->hideFields($user);
        }
        
        return $users;
    }
    
    /**
     * Activate user
     */
    public function activateUser($id)
    {
        return $this->update($id, ['status' => 'active']);
    }
    
    /**
     * Deactivate user
     */
    public function deactivateUser($id)
    {
        return $this->update($id, ['status' => 'inactive']);
    }
    
    /**
     * Get user statistics
     */
    public function getStatistics()
    {
        $stats = [];
        
        // Total users
        $stats['total'] = $this->count();
        
        // Active users
        $stats['active'] = $this->count(['status' => 'active']);
        
        // Inactive users
        $stats['inactive'] = $this->count(['status' => 'inactive']);
        
        // Admin users
        $stats['admins'] = $this->count(['role' => 'admin']);
        
        // Regular users
        $stats['users'] = $this->count(['role' => 'user']);
        
        return $stats;
    }
}
