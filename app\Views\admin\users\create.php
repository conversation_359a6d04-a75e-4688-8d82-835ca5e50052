<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="fas fa-user-plus me-2"></i>
        Create New User
    </h2>
    <a href="<?= $this->url('admin/users') ?>" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        Back to Users
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-edit me-2"></i>
                    User Information
                </h5>
            </div>
            <div class="card-body">
                <form id="createUserForm" class="ajax-form reset-on-success" 
                      action="<?= $this->url('admin/users/store') ?>" method="POST">
                    
                    <?= $this->csrfField() ?>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">
                                    <i class="fas fa-user me-1"></i>
                                    Full Name <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       placeholder="Enter full name" required>
                                <div class="form-text">Enter the user's full name</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>
                                    Email Address <span class="text-danger">*</span>
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       placeholder="Enter email address" required>
                                <div class="form-text">This will be used for login</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-1"></i>
                                    Password <span class="text-danger">*</span>
                                </label>
                                <input type="password" class="form-control" id="password" name="password" 
                                       placeholder="Enter password" required minlength="6">
                                <div class="form-text">Minimum 6 characters</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">
                                    <i class="fas fa-lock me-1"></i>
                                    Confirm Password <span class="text-danger">*</span>
                                </label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                       placeholder="Confirm password" required>
                                <div class="form-text">Re-enter the password</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="role" class="form-label">
                                    <i class="fas fa-user-tag me-1"></i>
                                    Role <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="role" name="role" required>
                                    <option value="">Select Role</option>
                                    <option value="user">User</option>
                                    <option value="admin">Administrator</option>
                                </select>
                                <div class="form-text">Choose user role and permissions</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status" class="form-label">
                                    <i class="fas fa-toggle-on me-1"></i>
                                    Status <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="status" name="status" required>
                                    <option value="">Select Status</option>
                                    <option value="active" selected>Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                                <div class="form-text">User account status</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end gap-2">
                        <a href="<?= $this->url('admin/users') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            Create User
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Help Card -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Help & Guidelines
                </h6>
            </div>
            <div class="card-body">
                <h6 class="text-primary">User Roles:</h6>
                <ul class="list-unstyled mb-3">
                    <li class="mb-2">
                        <span class="badge bg-primary me-2">User</span>
                        Basic user with limited access
                    </li>
                    <li class="mb-2">
                        <span class="badge bg-danger me-2">Admin</span>
                        Full administrative access
                    </li>
                </ul>
                
                <h6 class="text-primary">Password Requirements:</h6>
                <ul class="list-unstyled mb-3">
                    <li><i class="fas fa-check text-success me-2"></i>Minimum 6 characters</li>
                    <li><i class="fas fa-check text-success me-2"></i>Mix of letters and numbers recommended</li>
                    <li><i class="fas fa-check text-success me-2"></i>Special characters allowed</li>
                </ul>
                
                <h6 class="text-primary">Status Options:</h6>
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <span class="badge bg-success me-2">Active</span>
                        User can log in and access the system
                    </li>
                    <li class="mb-2">
                        <span class="badge bg-warning me-2">Inactive</span>
                        User account is disabled
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- Quick Actions Card -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    Quick Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="generatePassword()">
                        <i class="fas fa-key me-2"></i>
                        Generate Password
                    </button>
                    <button type="button" class="btn btn-outline-info btn-sm" onclick="fillSampleData()">
                        <i class="fas fa-magic me-2"></i>
                        Fill Sample Data
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Password confirmation validation
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity('Passwords do not match');
        this.classList.add('is-invalid');
    } else {
        this.setCustomValidity('');
        this.classList.remove('is-invalid');
    }
});

// Generate random password
function generatePassword() {
    const length = 12;
    const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";
    let password = "";
    
    for (let i = 0; i < length; i++) {
        password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    
    document.getElementById('password').value = password;
    document.getElementById('confirm_password').value = password;
    
    // Trigger validation
    document.getElementById('confirm_password').dispatchEvent(new Event('input'));
}

// Fill sample data for testing
function fillSampleData() {
    document.getElementById('name').value = 'John Doe';
    document.getElementById('email').value = '<EMAIL>';
    document.getElementById('password').value = 'password123';
    document.getElementById('confirm_password').value = 'password123';
    document.getElementById('role').value = 'user';
    document.getElementById('status').value = 'active';
    
    // Trigger validation
    document.getElementById('confirm_password').dispatchEvent(new Event('input'));
}

// Form validation before submit
document.getElementById('createUserForm').addEventListener('submit', function(e) {
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    
    if (password !== confirmPassword) {
        e.preventDefault();
        alert('Passwords do not match!');
        return false;
    }
    
    if (password.length < 6) {
        e.preventDefault();
        alert('Password must be at least 6 characters long!');
        return false;
    }
});
</script>
