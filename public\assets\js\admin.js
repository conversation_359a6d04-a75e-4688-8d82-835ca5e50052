/**
 * Zeppelin MVC Admin Panel JavaScript
 */

class ZeppelinAdmin {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeComponents();
        this.setupAjaxDefaults();
    }

    setupEventListeners() {
        // Sidebar toggle for mobile
        document.addEventListener('click', (e) => {
            if (e.target.matches('.sidebar-toggle')) {
                this.toggleSidebar();
            }
        });

        // Confirm delete actions
        document.addEventListener('click', (e) => {
            if (e.target.matches('.btn-delete, .delete-btn')) {
                e.preventDefault();
                this.confirmDelete(e.target);
            }
        });

        // Form submissions with AJAX
        document.addEventListener('submit', (e) => {
            if (e.target.matches('.ajax-form')) {
                e.preventDefault();
                this.handleAjaxForm(e.target);
            }
        });

        // Auto-hide alerts
        this.autoHideAlerts();
    }

    initializeComponents() {
        // Initialize tooltips
        this.initTooltips();
        
        // Initialize data tables
        this.initDataTables();
        
        // Add fade-in animation to cards
        this.addFadeInAnimation();
    }

    setupAjaxDefaults() {
        // Set default AJAX headers
        const token = document.querySelector('meta[name="csrf-token"]');
        if (token) {
            window.csrfToken = token.getAttribute('content');
        }
    }

    toggleSidebar() {
        const sidebar = document.querySelector('.sidebar');
        const mainContent = document.querySelector('.main-content');
        
        if (sidebar && mainContent) {
            sidebar.classList.toggle('show');
            mainContent.classList.toggle('sidebar-open');
        }
    }

    confirmDelete(element) {
        const message = element.getAttribute('data-message') || 'Are you sure you want to delete this item?';
        const url = element.getAttribute('href') || element.getAttribute('data-url');
        
        if (confirm(message)) {
            if (element.tagName === 'A') {
                window.location.href = url;
            } else {
                this.performDelete(url);
            }
        }
    }

    async performDelete(url) {
        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    _token: window.csrfToken
                })
            });

            const result = await response.json();
            
            if (result.success) {
                this.showToast('Success', result.message, 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                this.showToast('Error', result.message, 'error');
            }
        } catch (error) {
            this.showToast('Error', 'An error occurred while deleting the item.', 'error');
        }
    }

    async handleAjaxForm(form) {
        const formData = new FormData(form);
        const url = form.getAttribute('action');
        const method = form.getAttribute('method') || 'POST';

        // Show loading state
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Loading...';
        submitBtn.disabled = true;

        try {
            const response = await fetch(url, {
                method: method,
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();
            
            if (result.success) {
                this.showToast('Success', result.message, 'success');
                
                // Reset form if it's a create form
                if (form.classList.contains('reset-on-success')) {
                    form.reset();
                }
                
                // Redirect if specified
                if (result.redirect) {
                    setTimeout(() => {
                        window.location.href = result.redirect;
                    }, 1500);
                }
            } else {
                this.showToast('Error', result.message || 'An error occurred.', 'error');
                
                // Show field errors
                if (result.errors) {
                    this.showFieldErrors(form, result.errors);
                }
            }
        } catch (error) {
            this.showToast('Error', 'An error occurred while processing your request.', 'error');
        } finally {
            // Restore button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }

    showFieldErrors(form, errors) {
        // Clear previous errors
        form.querySelectorAll('.is-invalid').forEach(el => {
            el.classList.remove('is-invalid');
        });
        form.querySelectorAll('.invalid-feedback').forEach(el => {
            el.remove();
        });

        // Show new errors
        errors.forEach(error => {
            const field = form.querySelector(`[name="${error.field}"]`);
            if (field) {
                field.classList.add('is-invalid');
                
                const feedback = document.createElement('div');
                feedback.className = 'invalid-feedback';
                feedback.textContent = error.message;
                field.parentNode.appendChild(feedback);
            }
        });
    }

    showToast(title, message, type = 'info') {
        const toast = document.getElementById('toast');
        if (!toast) return;

        const toastHeader = toast.querySelector('.toast-header strong');
        const toastBody = toast.querySelector('.toast-body');
        
        if (toastHeader) toastHeader.textContent = title;
        if (toastBody) toastBody.textContent = message;

        // Set toast color based on type
        toast.className = `toast ${type === 'success' ? 'bg-success text-white' : 
                                  type === 'error' ? 'bg-danger text-white' : 
                                  'bg-info text-white'}`;

        // Show toast
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
    }

    autoHideAlerts() {
        const alerts = document.querySelectorAll('.alert[data-auto-hide]');
        alerts.forEach(alert => {
            const delay = parseInt(alert.getAttribute('data-auto-hide')) || 5000;
            setTimeout(() => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }, delay);
        });
    }

    initTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    initDataTables() {
        const tables = document.querySelectorAll('.data-table');
        tables.forEach(table => {
            // Add search functionality
            this.addTableSearch(table);
            
            // Add sorting functionality
            this.addTableSorting(table);
        });
    }

    addTableSearch(table) {
        const searchInput = table.parentNode.querySelector('.table-search');
        if (!searchInput) return;

        searchInput.addEventListener('input', (e) => {
            const searchTerm = e.target.value.toLowerCase();
            const rows = table.querySelectorAll('tbody tr');
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        });
    }

    addTableSorting(table) {
        const headers = table.querySelectorAll('th[data-sortable]');
        
        headers.forEach(header => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', () => {
                this.sortTable(table, header);
            });
        });
    }

    sortTable(table, header) {
        const columnIndex = Array.from(header.parentNode.children).indexOf(header);
        const rows = Array.from(table.querySelectorAll('tbody tr'));
        const isAscending = header.classList.contains('sort-asc');
        
        // Clear all sort classes
        table.querySelectorAll('th').forEach(th => {
            th.classList.remove('sort-asc', 'sort-desc');
        });
        
        // Add appropriate sort class
        header.classList.add(isAscending ? 'sort-desc' : 'sort-asc');
        
        // Sort rows
        rows.sort((a, b) => {
            const aText = a.children[columnIndex].textContent.trim();
            const bText = b.children[columnIndex].textContent.trim();
            
            const comparison = aText.localeCompare(bText, undefined, { numeric: true });
            return isAscending ? -comparison : comparison;
        });
        
        // Reorder rows in table
        const tbody = table.querySelector('tbody');
        rows.forEach(row => tbody.appendChild(row));
    }

    addFadeInAnimation() {
        const cards = document.querySelectorAll('.card');
        cards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
            card.classList.add('fade-in');
        });
    }

    // Utility methods
    static formatDate(date) {
        return new Date(date).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    static formatCurrency(amount) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    }

    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialize admin panel when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.zeppelinAdmin = new ZeppelinAdmin();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ZeppelinAdmin;
}
