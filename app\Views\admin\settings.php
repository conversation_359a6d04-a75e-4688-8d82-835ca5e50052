<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="fas fa-cog me-2"></i>
        System Settings
    </h2>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Profile Settings -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-cog me-2"></i>
                    Profile Settings
                </h5>
            </div>
            <div class="card-body">
                <form id="profileForm" class="ajax-form" action="<?= $this->url('admin/settings/update') ?>" method="POST">
                    <?= $this->csrfField() ?>
                    <input type="hidden" name="type" value="profile">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">
                                    <i class="fas fa-user me-1"></i>
                                    Full Name
                                </label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?= $this->escape($user['name']) ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>
                                    Email Address
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?= $this->escape($user['email']) ?>" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            Update Profile
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Password Settings -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-lock me-2"></i>
                    Change Password
                </h5>
            </div>
            <div class="card-body">
                <form id="passwordForm" class="ajax-form" action="<?= $this->url('admin/settings/update') ?>" method="POST">
                    <?= $this->csrfField() ?>
                    <input type="hidden" name="type" value="password">
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="current_password" class="form-label">
                                    <i class="fas fa-key me-1"></i>
                                    Current Password
                                </label>
                                <input type="password" class="form-control" id="current_password" name="current_password" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="new_password" class="form-label">
                                    <i class="fas fa-lock me-1"></i>
                                    New Password
                                </label>
                                <input type="password" class="form-control" id="new_password" name="new_password" 
                                       required minlength="6">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="confirm_new_password" class="form-label">
                                    <i class="fas fa-lock me-1"></i>
                                    Confirm Password
                                </label>
                                <input type="password" class="form-control" id="confirm_new_password" name="confirm_new_password" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end">
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-key me-2"></i>
                            Change Password
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- System Information -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    System Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Framework:</strong></td>
                                <td>Zeppelin MVC</td>
                            </tr>
                            <tr>
                                <td><strong>PHP Version:</strong></td>
                                <td><?= PHP_VERSION ?></td>
                            </tr>
                            <tr>
                                <td><strong>Server:</strong></td>
                                <td><?= $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown' ?></td>
                            </tr>
                            <tr>
                                <td><strong>Environment:</strong></td>
                                <td>
                                    <span class="badge bg-<?= APP_ENV === 'production' ? 'success' : 'warning' ?>">
                                        <?= strtoupper(APP_ENV) ?>
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Debug Mode:</strong></td>
                                <td>
                                    <span class="badge bg-<?= APP_DEBUG ? 'danger' : 'success' ?>">
                                        <?= APP_DEBUG ? 'ON' : 'OFF' ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Timezone:</strong></td>
                                <td><?= date_default_timezone_get() ?></td>
                            </tr>
                            <tr>
                                <td><strong>Memory Limit:</strong></td>
                                <td><?= ini_get('memory_limit') ?></td>
                            </tr>
                            <tr>
                                <td><strong>Upload Max:</strong></td>
                                <td><?= ini_get('upload_max_filesize') ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Quick Stats -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    Quick Stats
                </h6>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Disk Usage</span>
                    <span class="text-primary">
                        <?= round(disk_total_space('.') / 1024 / 1024 / 1024, 2) ?> GB
                    </span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Free Space</span>
                    <span class="text-success">
                        <?= round(disk_free_space('.') / 1024 / 1024 / 1024, 2) ?> GB
                    </span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>Server Time</span>
                    <span class="text-muted">
                        <?= date('Y-m-d H:i:s') ?>
                    </span>
                </div>
            </div>
        </div>

        <!-- Security -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    Security
                </h6>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>HTTPS</span>
                    <span class="badge bg-<?= isset($_SERVER['HTTPS']) ? 'success' : 'warning' ?>">
                        <?= isset($_SERVER['HTTPS']) ? 'Enabled' : 'Disabled' ?>
                    </span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Session Security</span>
                    <span class="badge bg-success">Enabled</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>CSRF Protection</span>
                    <span class="badge bg-success">Active</span>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-tools me-2"></i>
                    System Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-info btn-sm" onclick="clearCache()">
                        <i class="fas fa-broom me-2"></i>
                        Clear Cache
                    </button>
                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="exportData()">
                        <i class="fas fa-download me-2"></i>
                        Export Data
                    </button>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="checkUpdates()">
                        <i class="fas fa-sync me-2"></i>
                        Check Updates
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Password confirmation validation
document.getElementById('confirm_new_password').addEventListener('input', function() {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = this.value;
    
    if (newPassword !== confirmPassword) {
        this.setCustomValidity('Passwords do not match');
        this.classList.add('is-invalid');
    } else {
        this.setCustomValidity('');
        this.classList.remove('is-invalid');
    }
});

// System actions
function clearCache() {
    if (confirm('Are you sure you want to clear the cache?')) {
        // Implement cache clearing logic
        window.zeppelinAdmin.showToast('Success', 'Cache cleared successfully!', 'success');
    }
}

function exportData() {
    if (confirm('This will export all system data. Continue?')) {
        // Implement data export logic
        window.zeppelinAdmin.showToast('Info', 'Data export started...', 'info');
    }
}

function checkUpdates() {
    // Implement update checking logic
    window.zeppelinAdmin.showToast('Info', 'Checking for updates...', 'info');
    
    setTimeout(() => {
        window.zeppelinAdmin.showToast('Success', 'System is up to date!', 'success');
    }, 2000);
}
</script>
