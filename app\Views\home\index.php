<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .hero-card {
            background: white;
            border-radius: 2rem;
            box-shadow: 0 2rem 4rem rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .hero-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 2rem;
            text-align: center;
        }
        .hero-body {
            padding: 3rem 2rem;
        }
        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin: 0 auto 1rem;
        }
        .btn-admin {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 0.75rem 2rem;
            font-weight: 500;
            border-radius: 0.5rem;
            color: white;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        .btn-admin:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-2px);
            color: white;
        }
        .rocket-animation {
            animation: float 3s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="hero-card">
                    <div class="hero-header">
                        <div class="rocket-animation mb-3">
                            <i class="fas fa-rocket fa-4x"></i>
                        </div>
                        <h1 class="display-4 mb-3">Zeppelin MVC</h1>
                        <p class="lead mb-0">Modern PHP MVC Framework</p>
                    </div>
                    
                    <div class="hero-body">
                        <div class="text-center mb-5">
                            <h2 class="h3 mb-3"><?= $this->escape($message) ?></h2>
                            <p class="text-muted">
                                A lightweight, powerful, and easy-to-use PHP MVC framework with a beautiful admin panel.
                            </p>
                        </div>
                        
                        <div class="row g-4 mb-5">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <div class="feature-icon">
                                        <i class="fas fa-tachometer-alt"></i>
                                    </div>
                                    <h5>Fast & Lightweight</h5>
                                    <p class="text-muted">Optimized for performance with minimal overhead and clean architecture.</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <div class="feature-icon">
                                        <i class="fas fa-shield-alt"></i>
                                    </div>
                                    <h5>Secure by Default</h5>
                                    <p class="text-muted">Built-in security features including CSRF protection and XSS prevention.</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <div class="feature-icon">
                                        <i class="fas fa-mobile-alt"></i>
                                    </div>
                                    <h5>Responsive Design</h5>
                                    <p class="text-muted">Beautiful admin panel that works perfectly on all devices and screen sizes.</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row g-4 mb-5">
                            <div class="col-md-6">
                                <h5><i class="fas fa-check-circle text-success me-2"></i>Features</h5>
                                <ul class="list-unstyled">
                                    <li class="mb-2"><i class="fas fa-arrow-right text-primary me-2"></i>MVC Architecture</li>
                                    <li class="mb-2"><i class="fas fa-arrow-right text-primary me-2"></i>Routing System</li>
                                    <li class="mb-2"><i class="fas fa-arrow-right text-primary me-2"></i>Database ORM</li>
                                    <li class="mb-2"><i class="fas fa-arrow-right text-primary me-2"></i>Authentication</li>
                                    <li class="mb-2"><i class="fas fa-arrow-right text-primary me-2"></i>Admin Panel</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h5><i class="fas fa-cog text-info me-2"></i>Requirements</h5>
                                <ul class="list-unstyled">
                                    <li class="mb-2"><i class="fas fa-arrow-right text-primary me-2"></i>PHP 7.4+</li>
                                    <li class="mb-2"><i class="fas fa-arrow-right text-primary me-2"></i>MySQL 5.7+</li>
                                    <li class="mb-2"><i class="fas fa-arrow-right text-primary me-2"></i>Apache/Nginx</li>
                                    <li class="mb-2"><i class="fas fa-arrow-right text-primary me-2"></i>mod_rewrite</li>
                                    <li class="mb-2"><i class="fas fa-arrow-right text-primary me-2"></i>PDO Extension</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="text-center">
                            <a href="<?= $this->url('admin') ?>" class="btn-admin me-3">
                                <i class="fas fa-user-shield me-2"></i>
                                Access Admin Panel
                            </a>
                            <a href="https://github.com" class="btn btn-outline-secondary">
                                <i class="fab fa-github me-2"></i>
                                View on GitHub
                            </a>
                        </div>
                        
                        <div class="text-center mt-4">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Default admin credentials: <EMAIL> / admin123
                            </small>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <p class="text-white">
                        <i class="fas fa-heart text-danger"></i>
                        Made with love using Zeppelin MVC Framework
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
