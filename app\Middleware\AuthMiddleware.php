<?php

class AuthMiddleware
{
    public function handle()
    {
        // Check if user is authenticated
        if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_role'])) {
            // Redirect to login page
            header('Location: /admin/login');
            exit;
        }
        
        // Check if session is expired
        if (isset($_SESSION['last_activity'])) {
            $sessionLifetime = SESSION_LIFETIME * 60; // Convert to seconds
            if (time() - $_SESSION['last_activity'] > $sessionLifetime) {
                // Session expired
                session_destroy();
                header('Location: /admin/login?expired=1');
                exit;
            }
        }
        
        // Update last activity time
        $_SESSION['last_activity'] = time();
        
        return true;
    }
}
