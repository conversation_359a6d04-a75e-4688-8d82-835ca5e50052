<?php

// Database Migration Runner for Zeppelin MVC

// Define constants
define('ROOT_PATH', dirname(__DIR__));
define('APP_PATH', ROOT_PATH . '/app');
define('CONFIG_PATH', ROOT_PATH . '/config');

// Load configuration
require_once CONFIG_PATH . '/app.php';
require_once CONFIG_PATH . '/database.php';

// Autoloader
spl_autoload_register(function ($class) {
    $paths = [
        APP_PATH . '/Core/',
        APP_PATH . '/Models/',
        __DIR__ . '/seeds/',
    ];
    
    foreach ($paths as $path) {
        $file = $path . $class . '.php';
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
});

class MigrationRunner
{
    private $db;
    private $migrationsPath;
    private $seedsPath;
    
    public function __construct()
    {
        $this->migrationsPath = __DIR__ . '/migrations/';
        $this->seedsPath = __DIR__ . '/seeds/';
        
        try {
            $this->db = Database::getInstance();
            echo "✓ Database connection established\n";
        } catch (Exception $e) {
            echo "✗ Database connection failed: " . $e->getMessage() . "\n";
            echo "Please check your database configuration in .env file\n";
            exit(1);
        }
    }
    
    public function runMigrations()
    {
        echo "\n=== Running Database Migrations ===\n";
        
        // Create migrations table if it doesn't exist
        $this->createMigrationsTable();
        
        // Get all migration files
        $migrationFiles = glob($this->migrationsPath . '*.sql');
        sort($migrationFiles);
        
        if (empty($migrationFiles)) {
            echo "No migration files found.\n";
            return;
        }
        
        foreach ($migrationFiles as $file) {
            $filename = basename($file);
            
            // Check if migration has already been run
            if ($this->isMigrationRun($filename)) {
                echo "✓ Migration already run: {$filename}\n";
                continue;
            }
            
            echo "Running migration: {$filename}\n";
            
            // Read and execute SQL file
            $sql = file_get_contents($file);
            
            try {
                // Split SQL into individual statements
                $statements = array_filter(array_map('trim', explode(';', $sql)));
                
                foreach ($statements as $statement) {
                    if (!empty($statement)) {
                        $this->db->exec($statement);
                    }
                }
                
                // Mark migration as run
                $this->markMigrationAsRun($filename);
                echo "✓ Migration completed: {$filename}\n";
                
            } catch (PDOException $e) {
                echo "✗ Migration failed: {$filename}\n";
                echo "Error: " . $e->getMessage() . "\n";
                exit(1);
            }
        }
        
        echo "All migrations completed successfully!\n";
    }
    
    public function runSeeders()
    {
        echo "\n=== Running Database Seeders ===\n";
        
        // Get all seeder files
        $seederFiles = glob($this->seedsPath . '*Seeder.php');
        
        if (empty($seederFiles)) {
            echo "No seeder files found.\n";
            return;
        }
        
        foreach ($seederFiles as $file) {
            $filename = basename($file, '.php');
            $className = $filename;
            
            echo "Running seeder: {$className}\n";
            
            try {
                require_once $file;
                
                if (class_exists($className)) {
                    $seeder = new $className();
                    if (method_exists($seeder, 'run')) {
                        $seeder->run();
                    } else {
                        echo "✗ Seeder {$className} does not have a run() method\n";
                    }
                } else {
                    echo "✗ Seeder class {$className} not found\n";
                }
                
            } catch (Exception $e) {
                echo "✗ Seeder failed: {$className}\n";
                echo "Error: " . $e->getMessage() . "\n";
            }
        }
        
        echo "All seeders completed!\n";
    }
    
    private function createMigrationsTable()
    {
        $sql = "
            CREATE TABLE IF NOT EXISTS migrations (
                id INT AUTO_INCREMENT PRIMARY KEY,
                migration VARCHAR(255) NOT NULL,
                executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE KEY unique_migration (migration)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $this->db->exec($sql);
    }
    
    private function isMigrationRun($filename)
    {
        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM migrations WHERE migration = ?");
        $stmt->execute([$filename]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $result['count'] > 0;
    }
    
    private function markMigrationAsRun($filename)
    {
        $stmt = $this->db->prepare("INSERT INTO migrations (migration) VALUES (?)");
        $stmt->execute([$filename]);
    }
    
    public function reset()
    {
        echo "\n=== Resetting Database ===\n";
        
        if (!$this->confirmAction("This will drop all tables and data. Are you sure?")) {
            echo "Reset cancelled.\n";
            return;
        }
        
        try {
            // Drop all tables
            $this->db->exec("SET FOREIGN_KEY_CHECKS = 0");
            
            $stmt = $this->db->query("SHOW TABLES");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            foreach ($tables as $table) {
                $this->db->exec("DROP TABLE IF EXISTS `{$table}`");
                echo "✓ Dropped table: {$table}\n";
            }
            
            $this->db->exec("SET FOREIGN_KEY_CHECKS = 1");
            
            echo "Database reset completed!\n";
            
        } catch (PDOException $e) {
            echo "✗ Reset failed: " . $e->getMessage() . "\n";
        }
    }
    
    private function confirmAction($message)
    {
        echo $message . " (y/N): ";
        $handle = fopen("php://stdin", "r");
        $line = fgets($handle);
        fclose($handle);
        
        return trim(strtolower($line)) === 'y';
    }
}

// Command line interface
if (php_sapi_name() === 'cli') {
    $runner = new MigrationRunner();
    
    $command = $argv[1] ?? 'migrate';
    
    switch ($command) {
        case 'migrate':
            $runner->runMigrations();
            break;
            
        case 'seed':
            $runner->runSeeders();
            break;
            
        case 'fresh':
            $runner->reset();
            $runner->runMigrations();
            $runner->runSeeders();
            break;
            
        case 'reset':
            $runner->reset();
            break;
            
        default:
            echo "Usage: php migrate.php [command]\n";
            echo "Commands:\n";
            echo "  migrate  - Run pending migrations\n";
            echo "  seed     - Run database seeders\n";
            echo "  fresh    - Reset database and run migrations + seeders\n";
            echo "  reset    - Drop all tables\n";
            break;
    }
} else {
    echo "This script can only be run from command line.\n";
}
