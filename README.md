# 🚀 Zeppelin MVC Framework

Modern, lightweight, and secure PHP MVC framework with a beautiful admin panel.

## ✨ Features

- **MVC Architecture** - Clean separation of concerns
- **Routing System** - Flexible URL routing with parameters
- **Database ORM** - Simple and powerful database abstraction
- **Authentication** - Built-in user authentication and session management
- **Admin Panel** - Beautiful, responsive admin interface
- **Security** - CSRF protection, XSS prevention, and secure sessions
- **Middleware** - Request filtering and authentication middleware
- **Responsive Design** - Mobile-first admin panel design

## 📋 Requirements

- PHP 7.4 or higher
- MySQL 5.7 or higher
- Apache/Nginx with mod_rewrite
- PDO extension

## 🚀 Quick Start

### 1. Installation

```bash
# Clone or download the project to your web server
# For XAMPP: place in htdocs/zeppelin
# For other servers: place in your web root
```

### 2. Configuration

1. Copy `.env` file and update database settings:
```env
DB_HOST=localhost
DB_NAME=zeppelin_mvc
DB_USER=root
DB_PASS=your_password
```

2. Create database:
```sql
CREATE DATABASE zeppelin_mvc;
```

### 3. Database Setup

Run migrations and seeders:
```bash
cd database
php migrate.php fresh
```

This will:
- Create all necessary tables
- Insert default admin user
- Add sample data

### 4. Access the Application

- **Website**: `http://localhost/zeppelin/public`
- **Admin Panel**: `http://localhost/zeppelin/public/admin`

**Default Admin Credentials:**
- Email: `<EMAIL>`
- Password: `admin123`

## 📁 Project Structure

```
zeppelin/
├── app/
│   ├── Controllers/     # Application controllers
│   ├── Models/         # Database models
│   ├── Views/          # View templates
│   ├── Core/           # Framework core classes
│   └── Middleware/     # Request middleware
├── config/             # Configuration files
├── database/           # Migrations and seeders
├── public/             # Web accessible files
│   ├── assets/         # CSS, JS, images
│   └── index.php       # Entry point
├── storage/            # Logs and cache
└── vendor/             # Third-party packages
```

## 🎯 Usage Examples

### Creating a Controller

```php
<?php

class ProductController extends Controller
{
    public function index()
    {
        $products = $this->loadModel('Product')->findAll();
        $this->view('products/index', ['products' => $products]);
    }
    
    public function show($id)
    {
        $product = $this->loadModel('Product')->find($id);
        $this->view('products/show', ['product' => $product]);
    }
}
```

### Adding Routes

```php
// config/routes.php
$router->get('/products', 'ProductController@index');
$router->get('/products/{id}', 'ProductController@show');
$router->post('/products', 'ProductController@store', ['AuthMiddleware']);
```

### Creating a Model

```php
<?php

class Product extends Model
{
    protected $table = 'products';
    protected $fillable = ['name', 'price', 'description'];
    
    public function getActiveProducts()
    {
        return $this->where(['status' => 'active']);
    }
}
```

## 🔐 Security Features

- **CSRF Protection** - All forms include CSRF tokens
- **XSS Prevention** - Automatic output escaping
- **SQL Injection Protection** - Prepared statements
- **Session Security** - Secure session configuration
- **Password Hashing** - Bcrypt password hashing
- **Remember Me** - Secure remember me functionality

## 🎨 Admin Panel Features

- **Dashboard** - System overview and statistics
- **User Management** - CRUD operations for users
- **Role-based Access** - Admin and user roles
- **Responsive Design** - Works on all devices
- **Modern UI** - Bootstrap 5 with custom styling
- **AJAX Forms** - Smooth user experience
- **Real-time Notifications** - Toast notifications

## 🛠️ Development

### Database Commands

```bash
# Run migrations
php database/migrate.php migrate

# Run seeders
php database/migrate.php seed

# Fresh install (reset + migrate + seed)
php database/migrate.php fresh

# Reset database
php database/migrate.php reset
```

### Adding New Features

1. **Create Controller**: Add to `app/Controllers/`
2. **Create Model**: Add to `app/Models/`
3. **Create Views**: Add to `app/Views/`
4. **Add Routes**: Update `config/routes.php`
5. **Add Middleware**: Add to `app/Middleware/` if needed

## 📝 Configuration

### Environment Variables

```env
# Application
APP_NAME=Zeppelin MVC
APP_ENV=development
APP_DEBUG=true
APP_URL=http://localhost/zeppelin/public

# Database
DB_HOST=localhost
DB_NAME=zeppelin_mvc
DB_USER=root
DB_PASS=

# Security
APP_KEY=your-secret-key-here
SESSION_LIFETIME=120
```

### Web Server Configuration

#### Apache (.htaccess included)
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]
```

#### Nginx
```nginx
location / {
    try_files $uri $uri/ /index.php?$query_string;
}
```

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📄 License

This project is open-sourced software licensed under the MIT license.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Review the code examples

## 🎉 Credits

Built with ❤️ using:
- PHP
- Bootstrap 5
- Font Awesome
- MySQL

---

**Happy Coding! 🚀**
