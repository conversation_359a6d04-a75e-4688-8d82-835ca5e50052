<?php

class Controller
{
    protected $view;
    protected $model;
    
    public function __construct()
    {
        $this->view = new View();
    }
    
    /**
     * Load a model
     */
    protected function loadModel($model)
    {
        if (class_exists($model)) {
            return new $model();
        }
        throw new Exception("Model {$model} not found");
    }
    
    /**
     * Render a view
     */
    protected function view($viewName, $data = [])
    {
        $this->view->render($viewName, $data);
    }
    
    /**
     * Render JSON response
     */
    protected function json($data, $statusCode = 200)
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
    
    /**
     * Redirect to another URL
     */
    protected function redirect($url, $statusCode = 302)
    {
        http_response_code($statusCode);
        header("Location: {$url}");
        exit;
    }
    
    /**
     * Get POST data
     */
    protected function getPost($key = null, $default = null)
    {
        if ($key === null) {
            return $_POST;
        }
        return $_POST[$key] ?? $default;
    }
    
    /**
     * Get GET data
     */
    protected function getGet($key = null, $default = null)
    {
        if ($key === null) {
            return $_GET;
        }
        return $_GET[$key] ?? $default;
    }
    
    /**
     * Validate CSRF token
     */
    protected function validateCsrf()
    {
        $token = $this->getPost('_token');
        if (!$token || !hash_equals($_SESSION['_token'] ?? '', $token)) {
            http_response_code(403);
            die('CSRF token mismatch');
        }
    }
    
    /**
     * Generate CSRF token
     */
    protected function generateCsrfToken()
    {
        if (!isset($_SESSION['_token'])) {
            $_SESSION['_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['_token'];
    }
    
    /**
     * Check if user is authenticated
     */
    protected function requireAuth()
    {
        if (!isset($_SESSION['user_id'])) {
            $this->redirect('/admin/login');
        }
    }
    
    /**
     * Get current user
     */
    protected function getCurrentUser()
    {
        if (isset($_SESSION['user_id'])) {
            $userModel = $this->loadModel('User');
            return $userModel->find($_SESSION['user_id']);
        }
        return null;
    }
}
