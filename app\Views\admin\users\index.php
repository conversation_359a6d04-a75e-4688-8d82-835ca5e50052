<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="fas fa-users me-2"></i>
        Users Management
    </h2>
    <a href="<?= $this->url('admin/users/create') ?>" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        Add New User
    </a>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?= $this->url('admin/users') ?>" class="row g-3">
            <div class="col-md-6">
                <label for="search" class="form-label">Search Users</label>
                <input type="text" class="form-control" id="search" name="search" 
                       placeholder="Search by name or email..." value="<?= $this->escape($search) ?>">
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search me-2"></i>
                        Search
                    </button>
                </div>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <a href="<?= $this->url('admin/users') ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-2"></i>
                        Clear
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Users Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            All Users
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($users)): ?>
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No users found</h5>
                <p class="text-muted">
                    <?php if (!empty($search)): ?>
                        No users match your search criteria.
                    <?php else: ?>
                        Start by adding your first user.
                    <?php endif; ?>
                </p>
                <a href="<?= $this->url('admin/users/create') ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    Add First User
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover data-table">
                    <thead>
                        <tr>
                            <th data-sortable>ID</th>
                            <th data-sortable>Name</th>
                            <th data-sortable>Email</th>
                            <th data-sortable>Role</th>
                            <th data-sortable>Status</th>
                            <th data-sortable>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                            <tr>
                                <td><?= $this->escape($user['id']) ?></td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                            <i class="fas fa-user text-white"></i>
                                        </div>
                                        <strong><?= $this->escape($user['name']) ?></strong>
                                    </div>
                                </td>
                                <td><?= $this->escape($user['email']) ?></td>
                                <td>
                                    <span class="badge bg-<?= $user['role'] === 'admin' ? 'danger' : 'primary' ?>">
                                        <?= ucfirst($this->escape($user['role'])) ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?= $user['status'] === 'active' ? 'success' : 'warning' ?>">
                                        <?= ucfirst($this->escape($user['status'])) ?>
                                    </span>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?= date('M d, Y', strtotime($user['created_at'])) ?>
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= $this->url('admin/users/' . $user['id'] . '/edit') ?>" 
                                           class="btn btn-sm btn-outline-primary" 
                                           data-bs-toggle="tooltip" title="Edit User">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" 
                                                class="btn btn-sm btn-outline-danger delete-btn" 
                                                data-url="<?= $this->url('admin/users/' . $user['id'] . '/delete') ?>"
                                                data-message="Are you sure you want to delete <?= $this->escape($user['name']) ?>?"
                                                data-bs-toggle="tooltip" title="Delete User">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <nav aria-label="Users pagination" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <!-- Previous Page -->
                        <?php if ($currentPage > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="<?= $this->url('admin/users?page=' . ($currentPage - 1) . (!empty($search) ? '&search=' . urlencode($search) : '')) ?>">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        <?php endif; ?>

                        <!-- Page Numbers -->
                        <?php for ($i = max(1, $currentPage - 2); $i <= min($totalPages, $currentPage + 2); $i++): ?>
                            <li class="page-item <?= $i === $currentPage ? 'active' : '' ?>">
                                <a class="page-link" href="<?= $this->url('admin/users?page=' . $i . (!empty($search) ? '&search=' . urlencode($search) : '')) ?>">
                                    <?= $i ?>
                                </a>
                            </li>
                        <?php endfor; ?>

                        <!-- Next Page -->
                        <?php if ($currentPage < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="<?= $this->url('admin/users?page=' . ($currentPage + 1) . (!empty($search) ? '&search=' . urlencode($search) : '')) ?>">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>

                <div class="text-center text-muted">
                    <small>
                        Page <?= $currentPage ?> of <?= $totalPages ?>
                    </small>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<style>
.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 0.875rem;
}

.table th[data-sortable] {
    cursor: pointer;
    user-select: none;
    position: relative;
}

.table th[data-sortable]:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.table th[data-sortable]:after {
    content: '\f0dc';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.3;
}

.table th.sort-asc:after {
    content: '\f0de';
    opacity: 1;
}

.table th.sort-desc:after {
    content: '\f0dd';
    opacity: 1;
}
</style>
