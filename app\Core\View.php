<?php

class View
{
    private $viewPath;
    private $layoutPath;
    private $data = [];
    
    public function __construct()
    {
        $this->viewPath = APP_PATH . '/Views/';
        $this->layoutPath = APP_PATH . '/Views/layouts/';
    }
    
    /**
     * Render a view with optional layout
     */
    public function render($viewName, $data = [], $layout = null)
    {
        $this->data = $data;
        
        // Extract data to variables
        extract($data);
        
        // Start output buffering
        ob_start();
        
        // Include the view file
        $viewFile = $this->viewPath . $viewName . '.php';
        if (file_exists($viewFile)) {
            include $viewFile;
        } else {
            throw new Exception("View file not found: {$viewFile}");
        }
        
        // Get the view content
        $content = ob_get_clean();
        
        // If layout is specified, render with layout
        if ($layout) {
            $this->renderWithLayout($content, $layout, $data);
        } else {
            echo $content;
        }
    }
    
    /**
     * Render view with layout
     */
    private function renderWithLayout($content, $layout, $data = [])
    {
        // Extract data to variables
        extract($data);
        
        // Make content available to layout
        $this->data['content'] = $content;
        extract($this->data);
        
        $layoutFile = $this->layoutPath . $layout . '.php';
        if (file_exists($layoutFile)) {
            include $layoutFile;
        } else {
            throw new Exception("Layout file not found: {$layoutFile}");
        }
    }
    
    /**
     * Include a partial view
     */
    public function partial($partialName, $data = [])
    {
        extract($data);
        $partialFile = $this->viewPath . 'partials/' . $partialName . '.php';
        if (file_exists($partialFile)) {
            include $partialFile;
        } else {
            throw new Exception("Partial file not found: {$partialFile}");
        }
    }
    
    /**
     * Escape HTML output
     */
    public function escape($string)
    {
        return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Generate URL
     */
    public function url($path = '')
    {
        $baseUrl = $this->getBaseUrl();
        return $baseUrl . '/' . ltrim($path, '/');
    }
    
    /**
     * Generate asset URL
     */
    public function asset($path)
    {
        $baseUrl = $this->getBaseUrl();
        return $baseUrl . '/assets/' . ltrim($path, '/');
    }
    
    /**
     * Get base URL
     */
    private function getBaseUrl()
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        $basePath = dirname($_SERVER['SCRIPT_NAME']);
        
        return $protocol . '://' . $host . $basePath;
    }
    
    /**
     * Generate CSRF token field
     */
    public function csrfField()
    {
        $token = $_SESSION['_token'] ?? '';
        return '<input type="hidden" name="_token" value="' . $this->escape($token) . '">';
    }
    
    /**
     * Check if data exists
     */
    public function has($key)
    {
        return isset($this->data[$key]);
    }
    
    /**
     * Get data value
     */
    public function get($key, $default = null)
    {
        return $this->data[$key] ?? $default;
    }
}
