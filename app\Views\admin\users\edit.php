<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="fas fa-user-edit me-2"></i>
        Edit User: <?= $this->escape($user['name']) ?>
    </h2>
    <a href="<?= $this->url('admin/users') ?>" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        Back to Users
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-edit me-2"></i>
                    User Information
                </h5>
            </div>
            <div class="card-body">
                <form id="editUserForm" class="ajax-form" 
                      action="<?= $this->url('admin/users/' . $user['id'] . '/update') ?>" method="POST">
                    
                    <?= $this->csrfField() ?>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">
                                    <i class="fas fa-user me-1"></i>
                                    Full Name <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       placeholder="Enter full name" required
                                       value="<?= $this->escape($user['name']) ?>">
                                <div class="form-text">Enter the user's full name</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>
                                    Email Address <span class="text-danger">*</span>
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       placeholder="Enter email address" required
                                       value="<?= $this->escape($user['email']) ?>">
                                <div class="form-text">This will be used for login</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-1"></i>
                                    New Password
                                </label>
                                <input type="password" class="form-control" id="password" name="password" 
                                       placeholder="Leave blank to keep current password" minlength="6">
                                <div class="form-text">Leave blank to keep current password</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">
                                    <i class="fas fa-lock me-1"></i>
                                    Confirm New Password
                                </label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                       placeholder="Confirm new password">
                                <div class="form-text">Re-enter the new password</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="role" class="form-label">
                                    <i class="fas fa-user-tag me-1"></i>
                                    Role <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="role" name="role" required>
                                    <option value="">Select Role</option>
                                    <option value="user" <?= $user['role'] === 'user' ? 'selected' : '' ?>>User</option>
                                    <option value="admin" <?= $user['role'] === 'admin' ? 'selected' : '' ?>>Administrator</option>
                                </select>
                                <div class="form-text">Choose user role and permissions</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status" class="form-label">
                                    <i class="fas fa-toggle-on me-1"></i>
                                    Status <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="status" name="status" required>
                                    <option value="">Select Status</option>
                                    <option value="active" <?= $user['status'] === 'active' ? 'selected' : '' ?>>Active</option>
                                    <option value="inactive" <?= $user['status'] === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                                </select>
                                <div class="form-text">User account status</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end gap-2">
                        <a href="<?= $this->url('admin/users') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            Update User
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- User Info Card -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    User Details
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="avatar-lg bg-primary rounded-circle d-flex align-items-center justify-content-center mx-auto mb-2">
                        <i class="fas fa-user fa-2x text-white"></i>
                    </div>
                    <h6><?= $this->escape($user['name']) ?></h6>
                    <span class="badge bg-<?= $user['role'] === 'admin' ? 'danger' : 'primary' ?>">
                        <?= ucfirst($this->escape($user['role'])) ?>
                    </span>
                </div>
                
                <hr>
                
                <div class="row text-center">
                    <div class="col-6">
                        <div class="mb-2">
                            <i class="fas fa-calendar text-muted"></i>
                        </div>
                        <h6 class="mb-0">Created</h6>
                        <small class="text-muted">
                            <?= date('M d, Y', strtotime($user['created_at'])) ?>
                        </small>
                    </div>
                    <div class="col-6">
                        <div class="mb-2">
                            <i class="fas fa-sign-in-alt text-muted"></i>
                        </div>
                        <h6 class="mb-0">Last Login</h6>
                        <small class="text-muted">
                            <?= $user['last_login'] ? date('M d, Y', strtotime($user['last_login'])) : 'Never' ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions Card -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    Quick Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="generatePassword()">
                        <i class="fas fa-key me-2"></i>
                        Generate New Password
                    </button>
                    <?php if ($user['status'] === 'active'): ?>
                        <button type="button" class="btn btn-outline-warning btn-sm" onclick="toggleUserStatus('inactive')">
                            <i class="fas fa-user-times me-2"></i>
                            Deactivate User
                        </button>
                    <?php else: ?>
                        <button type="button" class="btn btn-outline-success btn-sm" onclick="toggleUserStatus('active')">
                            <i class="fas fa-user-check me-2"></i>
                            Activate User
                        </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-lg {
    width: 80px;
    height: 80px;
    font-size: 1.5rem;
}
</style>

<script>
// Password confirmation validation
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (password && password !== confirmPassword) {
        this.setCustomValidity('Passwords do not match');
        this.classList.add('is-invalid');
    } else {
        this.setCustomValidity('');
        this.classList.remove('is-invalid');
    }
});

// Generate random password
function generatePassword() {
    const length = 12;
    const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";
    let password = "";
    
    for (let i = 0; i < length; i++) {
        password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    
    document.getElementById('password').value = password;
    document.getElementById('confirm_password').value = password;
    
    // Trigger validation
    document.getElementById('confirm_password').dispatchEvent(new Event('input'));
}

// Toggle user status
function toggleUserStatus(status) {
    document.getElementById('status').value = status;
    
    if (confirm(`Are you sure you want to ${status === 'active' ? 'activate' : 'deactivate'} this user?`)) {
        document.getElementById('editUserForm').submit();
    }
}

// Form validation before submit
document.getElementById('editUserForm').addEventListener('submit', function(e) {
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    
    if (password && password !== confirmPassword) {
        e.preventDefault();
        alert('Passwords do not match!');
        return false;
    }
    
    if (password && password.length < 6) {
        e.preventDefault();
        alert('Password must be at least 6 characters long!');
        return false;
    }
});
</script>
